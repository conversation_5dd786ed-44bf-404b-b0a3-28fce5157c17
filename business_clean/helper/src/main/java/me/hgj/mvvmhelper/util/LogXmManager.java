package me.hgj.mvvmhelper.util;

import android.content.Context;
import android.os.Environment;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import me.hgj.mvvmhelper.base.Ktx;

/**
 * 日志记录文件
 */
public class LogXmManager {
    private static final String LOG_FILE_NAME = "app_xm_clean.log";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static void log(String message) {
        File logDir = new File(Ktx.app.getBaseContext().getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "logs");
        if (!logDir.exists()) {
            logDir.mkdirs();
        }
        File logFile = new File(logDir, LOG_FILE_NAME);

        try (FileWriter writer = new FileWriter(logFile, true)) {
            String timestamp = DATE_FORMAT.format(new Date());
            writer.append(timestamp).append(" - ").append(message).append("\n");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String getLogsText(Context context) {
        StringBuilder logs = new StringBuilder();
        File logDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "logs");
        File logFile = new File(logDir, LOG_FILE_NAME);

        if (logFile.exists()) {
            try {
                java.util.Scanner scanner = new java.util.Scanner(logFile);
                while (scanner.hasNextLine()) {
                    logs.append(scanner.nextLine()).append("\n");
                }
                scanner.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return logs.toString();
    }

    public static String getLogFilePath(Context context) {
        File logDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "logs");
        File logFile = new File(logDir, LOG_FILE_NAME);
        return logFile.getAbsolutePath();
    }

    // 新增方法：清空日志文件内容
    public static void clearLogFile(Context context) {
        File logDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "logs");
        File logFile = new File(logDir, LOG_FILE_NAME);

        try {
            if (logFile.exists()) {
                // 创建一个空文件并覆盖原文件
                new FileWriter(logFile, false).close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
