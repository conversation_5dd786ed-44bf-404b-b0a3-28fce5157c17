package com.business_clean.app.util;

import android.graphics.Bitmap;
import android.graphics.Canvas;

import com.blankj.utilcode.util.LogUtils;

/**
 * Bitmap安全操作工具类
 * 用于避免"Canvas: trying to use a recycled bitmap"错误
 */
public class BitmapSafetyHelper {
    private static final String TAG = "BitmapSafetyHelper";

    /**
     * 检查Bitmap是否可用（非空且未被回收）
     * @param bitmap 要检查的Bitmap
     * @return 是否可用
     */
    public static boolean isBitmapValid(Bitmap bitmap) {
        return bitmap != null && !bitmap.isRecycled();
    }

    /**
     * 安全地回收Bitmap
     * @param bitmap 要回收的Bitmap
     */
    public static void safeRecycleBitmap(Bitmap bitmap) {
        try {
            if (bitmap != null && !bitmap.isRecycled()) {
                bitmap.recycle();
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "回收Bitmap时出错: " + e.getMessage());
        }
    }

    /**
     * 安全地创建Bitmap副本
     * @param source 源Bitmap
     * @param config Bitmap配置
     * @param isMutable 是否可变
     * @return 副本Bitmap，失败时返回null
     */
    public static Bitmap safeCopyBitmap(Bitmap source, Bitmap.Config config, boolean isMutable) {
        if (!isBitmapValid(source)) {
            LogUtils.e(TAG, "源Bitmap无效，无法创建副本");
            return null;
        }
        
        try {
            return source.copy(config, isMutable);
        } catch (Exception e) {
            LogUtils.e(TAG, "创建Bitmap副本时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 安全地在Canvas上绘制Bitmap
     * @param canvas Canvas对象
     * @param bitmap 要绘制的Bitmap
     * @param left 左边距
     * @param top 上边距
     * @return 是否绘制成功
     */
    public static boolean safeDrawBitmap(Canvas canvas, Bitmap bitmap, float left, float top) {
        if (canvas == null) {
            LogUtils.e(TAG, "Canvas为空，无法绘制");
            return false;
        }
        
        if (!isBitmapValid(bitmap)) {
            LogUtils.e(TAG, "Bitmap无效，跳过绘制");
            return false;
        }
        
        try {
            canvas.drawBitmap(bitmap, left, top, null);
            return true;
        } catch (Exception e) {
            LogUtils.e(TAG, "绘制Bitmap时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 安全地创建Canvas
     * @param bitmap 目标Bitmap
     * @return Canvas对象，失败时返回null
     */
    public static Canvas safeCreateCanvas(Bitmap bitmap) {
        if (!isBitmapValid(bitmap)) {
            LogUtils.e(TAG, "Bitmap无效，无法创建Canvas");
            return null;
        }
        
        try {
            return new Canvas(bitmap);
        } catch (Exception e) {
            LogUtils.e(TAG, "创建Canvas时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 安全地获取Bitmap的宽度
     * @param bitmap Bitmap对象
     * @return 宽度，失败时返回0
     */
    public static int safeBitmapWidth(Bitmap bitmap) {
        if (!isBitmapValid(bitmap)) {
            return 0;
        }
        
        try {
            return bitmap.getWidth();
        } catch (Exception e) {
            LogUtils.e(TAG, "获取Bitmap宽度时出错: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 安全地获取Bitmap的高度
     * @param bitmap Bitmap对象
     * @return 高度，失败时返回0
     */
    public static int safeBitmapHeight(Bitmap bitmap) {
        if (!isBitmapValid(bitmap)) {
            return 0;
        }
        
        try {
            return bitmap.getHeight();
        } catch (Exception e) {
            LogUtils.e(TAG, "获取Bitmap高度时出错: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 安全地创建指定大小的Bitmap
     * @param width 宽度
     * @param height 高度
     * @param config Bitmap配置
     * @return 新创建的Bitmap，失败时返回null
     */
    public static Bitmap safeCreateBitmap(int width, int height, Bitmap.Config config) {
        if (width <= 0 || height <= 0) {
            LogUtils.e(TAG, "无效的Bitmap尺寸: " + width + "x" + height);
            return null;
        }
        
        try {
            return Bitmap.createBitmap(width, height, config);
        } catch (Exception e) {
            LogUtils.e(TAG, "创建Bitmap时出错: " + e.getMessage());
            return null;
        } catch (OutOfMemoryError e) {
            LogUtils.e(TAG, "创建Bitmap时内存不足: " + e.getMessage());
            return null;
        }
    }

    /**
     * 安全地缩放Bitmap
     * @param source 源Bitmap
     * @param width 目标宽度
     * @param height 目标高度
     * @param filter 是否使用过滤器
     * @return 缩放后的Bitmap，失败时返回null
     */
    public static Bitmap safeScaleBitmap(Bitmap source, int width, int height, boolean filter) {
        if (!isBitmapValid(source)) {
            LogUtils.e(TAG, "源Bitmap无效，无法缩放");
            return null;
        }
        
        if (width <= 0 || height <= 0) {
            LogUtils.e(TAG, "无效的缩放尺寸: " + width + "x" + height);
            return null;
        }
        
        try {
            return Bitmap.createScaledBitmap(source, width, height, filter);
        } catch (Exception e) {
            LogUtils.e(TAG, "缩放Bitmap时出错: " + e.getMessage());
            return null;
        } catch (OutOfMemoryError e) {
            LogUtils.e(TAG, "缩放Bitmap时内存不足: " + e.getMessage());
            return null;
        }
    }

    /**
     * 检查Bitmap是否需要回收（大于指定大小）
     * @param bitmap Bitmap对象
     * @param maxSizeBytes 最大允许大小（字节）
     * @return 是否需要回收
     */
    public static boolean shouldRecycleBitmap(Bitmap bitmap, long maxSizeBytes) {
        if (!isBitmapValid(bitmap)) {
            return false;
        }
        
        try {
            long bitmapSize = bitmap.getByteCount();
            return bitmapSize > maxSizeBytes;
        } catch (Exception e) {
            LogUtils.e(TAG, "检查Bitmap大小时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取Bitmap的内存占用大小
     * @param bitmap Bitmap对象
     * @return 内存占用大小（字节），失败时返回0
     */
    public static long getBitmapSize(Bitmap bitmap) {
        if (!isBitmapValid(bitmap)) {
            return 0;
        }
        
        try {
            return bitmap.getByteCount();
        } catch (Exception e) {
            LogUtils.e(TAG, "获取Bitmap大小时出错: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 批量安全回收Bitmap数组
     * @param bitmaps Bitmap数组
     */
    public static void safeRecycleBitmaps(Bitmap... bitmaps) {
        if (bitmaps == null) {
            return;
        }
        
        for (Bitmap bitmap : bitmaps) {
            safeRecycleBitmap(bitmap);
        }
    }

    /**
     * 记录Bitmap信息（用于调试）
     * @param bitmap Bitmap对象
     * @param tag 标签
     */
    public static void logBitmapInfo(Bitmap bitmap, String tag) {
        if (bitmap == null) {
            LogUtils.d(TAG, tag + ": Bitmap is null");
            return;
        }
        
        if (bitmap.isRecycled()) {
            LogUtils.d(TAG, tag + ": Bitmap is recycled");
            return;
        }
        
        try {
            LogUtils.d(TAG, tag + ": " + bitmap.getWidth() + "x" + bitmap.getHeight() + 
                ", size=" + bitmap.getByteCount() + " bytes, config=" + bitmap.getConfig());
        } catch (Exception e) {
            LogUtils.e(TAG, "记录Bitmap信息时出错: " + e.getMessage());
        }
    }
}
