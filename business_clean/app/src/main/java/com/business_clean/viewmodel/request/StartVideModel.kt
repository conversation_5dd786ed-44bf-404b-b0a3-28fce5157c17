package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.network.NetUrl
import com.business_clean.data.initconfig.MapInfo
import com.business_clean.data.initconfig.TimestampEntity
import com.business_clean.data.mode.login.CodeEntity
import com.business_clean.data.mode.login.UserInfo
import com.business_clean.data.mode.project.WorkRulesEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.logD
import me.hgj.mvvmhelper.ext.logE
import me.hgj.mvvmhelper.ext.rxHttpRequest
import me.hgj.mvvmhelper.ext.toast
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 启动界面获取
 */
class StartVideModel : BaseViewModel() {

    ///map的info
    var mapInfo = MutableLiveData<MapInfo>()

    var timestampEntity = MutableLiveData<TimestampEntity>()

    var errorEntity = MutableLiveData<Any>()
    var errorMapEntity = MutableLiveData<Any>()

    var workRulesErrorEntity = MutableLiveData<Any>()


    //获取用工规则
    var workRulesEntity = MutableLiveData<WorkRulesEntity>()

    /**
     * 拉取服务器时间 单独设置超时时间
     */
    fun requestTime() {
        rxHttpRequest {
            onRequest = {
                timestampEntity.value = RxHttp
                    .get(NetUrl.GET_TIME_STAMP)
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<TimestampEntity>().await()
            }
            onError = {
                errorEntity.value = true
            }
        }
    }


    /**
     * 动态拉取高德地图服务的Key
     */
    fun requestAMapInfo() {
        rxHttpRequest {
            onRequest = {
                mapInfo.value = RxHttp
                    .get(NetUrl.GET_A_MAP_KEY)
                    .add("map_code", "gao_de")
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<MapInfo>().await()
            }
            onError = {
                errorMapEntity.value = true
            }
        }
    }

    /**
     * 获取企业规则的配置
     */
    fun requestWorkRules() {
        rxHttpRequest {
            onRequest = {
                workRulesEntity.value = RxHttp.get(NetUrl.GET_WORK_POST_RULES)
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<WorkRulesEntity>().await()
            }
            onError = {
                workRulesErrorEntity.value = true
            }
        }
    }
}