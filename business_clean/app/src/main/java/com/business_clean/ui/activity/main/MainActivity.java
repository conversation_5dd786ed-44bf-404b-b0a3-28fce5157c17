package com.business_clean.ui.activity.main;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;

import android.Manifest;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.widget.Toast;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.MarketUtils;
import com.business_clean.app.util.TimeClockUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.appupdate.AppCheckUpdateUtil;
import com.business_clean.app.util.permission.PermissionInterceptor;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.data.initconfig.MapInfo;
import com.business_clean.data.initconfig.TimestampEntity;
import com.business_clean.data.mode.appupdate.DialogAlertNoticeInfo;
import com.business_clean.data.mode.appupdate.NewVersionInfo;
import com.business_clean.data.mode.init.InitDataEntity;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.data.mode.project.WorkRulesEntity;
import com.business_clean.data.mode.todo.TodoTotalEntity;
import com.business_clean.databinding.ActivityMainBinding;
import com.business_clean.ui.activity.CheckPermissionActivity;
import com.business_clean.ui.activity.camera.WatermarkCameraActivity;
import com.business_clean.ui.adapter.viewpager2.FragmentLazyStateAdapter;
import com.business_clean.ui.fragment.main.CameraFragment;
import com.business_clean.ui.fragment.main.RosterFragment;
import com.business_clean.ui.fragment.main.TodoFragment;
import com.business_clean.ui.fragment.main.WorkbenchFragment;
import com.business_clean.ui.fragment.main.WorkingCircleFragment;
import com.business_clean.viewmodel.request.MainViewModel;
import com.chaychan.library.BottomBarItem;
import com.chaychan.library.BottomBarLayout;
import com.chaychan.library.TabData;
import com.google.gson.Gson;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import me.hgj.mvvmhelper.ext.AppExtKt;
import me.hgj.mvvmhelper.ext.LogExtKt;
import me.hgj.mvvmhelper.net.manager.NetState;
import me.hgj.mvvmhelper.util.LogXmManager;

public class MainActivity extends BaseActivity<MainViewModel, ActivityMainBinding> {

    private int[] mIconUnselectIds = {
            R.mipmap.icon_home_workbench_unselected, R.mipmap.icon_home_todo_unselected,
            R.mipmap.icon_home_camera_unselected, R.mipmap.icon_home_circle_unselected, R.mipmap.icon_home_roster_unselected};

    private int[] mIconSelectIds = {
            R.mipmap.icon_home_workbench_selected, R.mipmap.icon_home_todo_selected,
            R.mipmap.icon_home_camera_unselected, R.mipmap.icon_home_circle_selected, R.mipmap.icon_home_roster_selected};


    private static final long TIME_INTERVAL = 2000; // 两次点击间的最大时间间隔
    private long backPressedTime;

    private boolean openCamera = false;

    @Override
    public boolean showToolBar() {
        return false;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            openCamera = getIntent().getExtras().getBoolean("openCamera");
        }
        //从本地拿用户的数据
        initUserConfig();

        ArrayList<Fragment> mFragments = new ArrayList<>();
        //在这里做权限的处理
        mFragments.add(new WorkingCircleFragment());
        mFragments.add(new TodoFragment());
        mFragments.add(new CameraFragment());
        mFragments.add(new RosterFragment());
        mFragments.add(new WorkbenchFragment());

        mDatabind.viewPager2.setUserInputEnabled(false);
        mDatabind.viewPager2.setOffscreenPageLimit(mFragments.size());
        mDatabind.viewPager2.setAdapter(new FragmentLazyStateAdapter(this, mFragments));

        List<TabData> tabData = new ArrayList<>();
        tabData.add(new TabData("现场", R.mipmap.icon_home_circle_unselected, R.mipmap.icon_home_circle_selected));
        tabData.add(new TabData("待办", R.mipmap.icon_home_todo_unselected, R.mipmap.icon_home_todo_selected));
//        tabData.add(new TabData("拍照", R.mipmap.icon_home_camera_unselected, R.mipmap.icon_home_camera_unselected));
        tabData.add(new TabData("花名册", R.mipmap.icon_home_roster_unselected, R.mipmap.icon_home_roster_selected));
        tabData.add(new TabData("工作台", R.mipmap.icon_home_workbench_unselected, R.mipmap.icon_home_workbench_selected));

        mDatabind.bottomLayout.setData(tabData);
        mDatabind.bottomLayout.setViewPager2(mDatabind.viewPager2);


        mDatabind.bottomLayout.postDelayed(new Runnable() {
            @Override
            public void run() {
                boolean aBoolean = MMKVHelper.getBoolean(ConstantMMVK.JUMP_TODO_PAGE, false);
                if (aBoolean) {
                    mDatabind.bottomLayout.setCurrentItem(1);
                    mDatabind.viewPager2.setCurrentItem(1);
                }
            }
        }, 3000);

        mViewModel.requestTodoTotal();//产品要求，每次点击下方底部 bar 的时候去请求待办的接口，更新未读消息
        //请求源数据
        mViewModel.requestInitData();
        mViewModel.requestCheckApp();
        //获取地图的key
        mViewModel.requestAMapInfo();
        //只有总部成员请求接口
        if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR || Constant.ROLE_REGIONAL_MANAGER) {
            mViewModel.requestHeadProject();//获取企业总部
        }


        //是否需要跳转
        if (openCamera) {
            mDatabind.viewPager2.postDelayed(new Runnable() {
                @Override
                public void run() {
                    ActivityForwardUtil.startActivity(CheckPermissionActivity.class);
                }
            }, 1000);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    /**
     * 从本地取值 设置默认值
     */
    private void initUserConfig() {
//        String userInfoJson = MMKVHelper.getString(ConstantMMVK.USER_INFO);
//        if (!TextUtils.isEmpty(userInfoJson)) {
//            UserInfo userInfo = new Gson().fromJson(userInfoJson, UserInfo.class);
//            CommonUtils.updateLocalUserData(userInfo);
//        }
    }

    @Override
    public void initObserver() {
        App.getAppViewModelInstance().getTodoMeCreate().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mDatabind.bottomLayout.setCurrentItem(1);
            }
        });

        App.getAppViewModelInstance().getBackPosition().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer position) {
                mDatabind.bottomLayout.setCurrentItem(position);
            }
        });

        //添加员工的监听  也需要去刷新角标
        App.getAppViewModelInstance().getRefreshTodoTotal().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity integer) {//更新未读消息
                mDatabind.bottomLayout.setUnread(1, Integer.parseInt(integer.getTotal()));
            }
        });
        App.getAppViewModelInstance().getRefreshTodo().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mViewModel.requestTodoTotal();
            }
        });

        App.getAppViewModelInstance().getRefreshMember().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mViewModel.requestTodoTotal();
            }
        });
        App.getAppViewModelInstance().getRefreshTime().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mViewModel.requestTime();
            }
        });
    }


    @Override
    public void onBindViewClick() {
        //item拦截 后期如果开启了收费模块，可以做处理
        mDatabind.bottomLayout.setOnPageChangeInterceptor(new BottomBarLayout.OnPageChangeInterceptor() {
            @Override
            public boolean onIntercepted(int position) {
                if (position == 2) {
                    //再判断当前选中的项目 如果是全部就让选择项目
                    if (App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getProject() != null
                            && TextUtils.isEmpty(App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid())) {
                        ///让选择完项目后，再去CheckPermissionActivity
                        boolean isHeadOffice = Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR || Constant.ROLE_REGIONAL_MANAGER;
                        CommonUtils.showSelectProjectDialog(MainActivity.this, "", true, false, isHeadOffice, new PagerDrawerPopup.OnSelectProjectListener() {
                            @Override
                            public void onClick(String projectUuid, String projectName) {
                                ActivityForwardUtil.startActivity(CheckPermissionActivity.class);
                            }
                        });
                    } else {
                        ActivityForwardUtil.startActivity(CheckPermissionActivity.class);
                    }
                    return true;
                }
                return false;
            }
        });

        //item 选中监听
        mDatabind.bottomLayout.setOnItemSelectedListener(new BottomBarLayout.OnItemSelectedListener() {
            @Override
            public void onItemSelected(BottomBarItem bottomBarItem, int previousPosition, int currentPosition) {
                //previousPosition 是上一个的下标，currentPosition 是现在下标
                LogExtKt.logE("previousPosition - " + previousPosition + " ; currentPosition - " + currentPosition, "");
                mViewModel.requestTodoTotal();//产品要求，每次点击下方底部 bar 的时候去请求待办的接口，更新未读消息
            }
        });
    }

    @Override
    public void onRequestSuccess() {
        ///线上获取地图的key
        mViewModel.getMapInfo().observe(this, new Observer<MapInfo>() {
            @Override
            public void onChanged(MapInfo mapInfo) {
                MMKVHelper.putString(ConstantMMVK.A_MAP_KEY, mapInfo.getMap_value());
                CommonUtils.onDelayMapInit(MainActivity.this);
            }
        });
        //总部成员的
        mViewModel.getProjectHeadOfficeMangerList().observe(this, new Observer<ProjectMangerList>() {
            @Override
            public void onChanged(ProjectMangerList projectMangerList) {
                //如果是大区经理，并且Uuid 是空的话 就默认个i总部的
                if (Constant.ROLE_REGIONAL_MANAGER && App.getAppViewModelInstance().getProjectInfo().getValue() != null && TextUtils.isEmpty(App.getAppViewModelInstance().getProjectInfo().getValue().getUuid())) {
                    App.getAppViewModelInstance().getProjectInfo().postValue(projectMangerList);
                    Objects.requireNonNull(App.getAppViewModelInstance().getUserInfo().getValue()).setProject(projectMangerList);
                }
            }
        });

        //用工规则
        mViewModel.getWorkRulesEntity().observe(this, new Observer<WorkRulesEntity>() {
            @Override
            public void onChanged(WorkRulesEntity workRulesEntity) {
                CommonUtils.updateCompanyConfig(workRulesEntity);
            }
        });

        //源数据接口，把内容都存储起来
        mViewModel.getInitData().observe(this, new Observer<InitDataEntity>() {
            @Override
            public void onChanged(InitDataEntity initDataEntity) {
                MMKVHelper.putString(ConstantMMVK.INIT_DATA, JSON.toJSONString(initDataEntity));
                MMKVHelper.putString(ConstantMMVK.IN_CLASS_TIME_LIST, JSON.toJSONString(initDataEntity.getIn_class_time_list()));
                MMKVHelper.putString(ConstantMMVK.OUT_CLASS_TIME_LIST, JSON.toJSONString(initDataEntity.getOut_class_time_list()));
            }
        });

        //设置角标
        mViewModel.getTodoTotal().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity todoList) {
                if (!Constant.ROLE_LEADER || !Constant.ROLE_CLEANER) {
                    App.getAppViewModelInstance().getRefreshTodoTotal().setValue(todoList);
                }
            }
        });
        //设置本地时间
        mViewModel.getTimestampEntity().observe(this, new Observer<TimestampEntity>() {
            @Override
            public void onChanged(TimestampEntity timestampEntity) {
                if (timestampEntity != null && !TextUtils.isEmpty(timestampEntity.getTimestamp())) {
                    MMKVHelper.putString(ConstantMMVK.TIME, "" + (Long.parseLong(timestampEntity.getTimestamp()) * 1000));
                    MMKVHelper.putString(ConstantMMVK.TIME_INIT_BOOT, "" + TimeClockUtil.getCurrentSystemUptimeInMillis());
                }
            }
        });

        mViewModel.getErrorEntity().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                //错误了，删除本地的时间
                MMKVHelper.remove(ConstantMMVK.TIME);
                MMKVHelper.remove(ConstantMMVK.TIME_INIT_BOOT);
            }
        });

        //更新的操作
        mViewModel.getNewVersionInfo().observe(this, new Observer<NewVersionInfo>() {
            @Override
            public void onChanged(NewVersionInfo newVersionInfo) {
                if (newVersionInfo == null) {
                    return;
                }
                // 获取发布品牌 ID 列表
                List<String> brandIdList = null;
                if (newVersionInfo.getBrand_id_list() != null && newVersionInfo.getBrand_id_list().size() > 0) {
                    brandIdList = newVersionInfo.getBrand_id_list();
                }

                // 获取当前手机厂商并映射为品牌 ID
                String manufacturer = Build.MANUFACTURER.toLowerCase(); // 转为小写
                String currentBrandId = CommonUtils.getBrandIdByManufacturer(manufacturer);


                //先判断是不是本次忽略的版本 如果是忽略版本，那么就直接return
                if (!TextUtils.isEmpty(newVersionInfo.getVersion()) && CommonUtils.isVersionIgnored(Integer.parseInt(newVersionInfo.getVersion().replace(".", "")))) {
                    return;
                }

                // 判断当前手机厂商是否在发布品牌列表中
                if (brandIdList != null && brandIdList.contains(currentBrandId)) {
                    // 符合条件：弹窗提示用户更新 跳转应用市场
                    showUpdateDialog(newVersionInfo);
                } else {
                    // 不符合条件：静默下载
                    AppCheckUpdateUtil.checkVersionNew(false, true);
                }
            }
        });
    }

    /**
     * 显示一个升级弹窗
     *
     * @param newVersionInfo
     */
    private void showUpdateDialog(NewVersionInfo newVersionInfo) {
        DialogAlertNoticeInfo dialogAlertNoticeInfo = new DialogAlertNoticeInfo();
        dialogAlertNoticeInfo.setTitle(newVersionInfo.getVersion() + "版本已经准备好了");
        dialogAlertNoticeInfo.setDescribe("升级一下新功能，也许能帮助到您");
        dialogAlertNoticeInfo.setContent(newVersionInfo.getDesc());
        dialogAlertNoticeInfo.setSub_title_color(ContextCompat.getColor(this, R.color.base_primary_warning));
        dialogAlertNoticeInfo.setConfirm_button("立即更新");
        dialogAlertNoticeInfo.setClose_button("取消");//即代表忽略本次版本更新
        dialogAlertNoticeInfo.setDismissOnBackPressed(false);
        dialogAlertNoticeInfo.setDismissOnTouchOutside(false);

        CommonUtils.showSystemUpdateAlertPop(this, dialogAlertNoticeInfo, new OnDialogCancelListener() {
            @Override
            public void onCancel() {
                if (!TextUtils.isEmpty(newVersionInfo.getVersion())) {
                    MMKVHelper.putInt(ConstantMMVK.APP_UPDATE_VERSION_CODE, Integer.parseInt(newVersionInfo.getVersion().replace(".", "")));
                }
            }
        }, new OnDialogConfirmListener() {
            @Override
            public void onConfirm() {
                MarketUtils.getTools().startMarket(MainActivity.this);
            }
        });
    }


    @Override
    protected void onStop() {
        super.onStop();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (backPressedTime + TIME_INTERVAL > System.currentTimeMillis()) {
                AppExtKt.finishAllActivity();
                System.exit(0);
            } else {
                Toast.makeText(this, "再次点击返回按钮退出应用", Toast.LENGTH_SHORT).show();
            }

            backPressedTime = System.currentTimeMillis();
            return false;
        } else {
            return super.onKeyDown(keyCode, event);
        }
    }

    @Override
    public void onNetworkStateChanged(@NonNull NetState netState) {
        super.onNetworkStateChanged(netState);
        if (netState.isSuccess()) {
            ToastUtil.show("有网络了");
            LogXmManager.log("MainActivity 有网络了");
        } else {
            ToastUtil.show("断网了，请检查网络");
            LogXmManager.log("MainActivity 断网了，请检查网络");
        }
    }
}