package com.business_clean.ui.activity.camera;

import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;

import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.config.AsyncRequestUtil;
import com.business_clean.app.config.Constant;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.databinding.ActivityCameraSettingBinding;
import com.business_clean.viewmodel.request.CameraViewModel;

import org.jetbrains.annotations.Nullable;

import java.util.HashMap;

/**
 * 拍照设置
 * 1.照片保存到手机相册 控制底部2个
 * 2.默认开启 保存手机相册 、 保存水印照片  原始
 * 3.水印照片是默认必须保存的
 */
public class CameraSettingActivity extends BaseActivity<CameraViewModel, ActivityCameraSettingBinding> {

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        //标记图片
        mDatabind.switchMark.setChecked(Constant.CAMERA_MARK_PHOTO);
        //是否保存图片/视频
        mDatabind.switchSaveSystemPic.setChecked(Constant.CAMERA_SAVE_PHOTO_VIDEO);
        //是否保存图片到本地
        mDatabind.switchSavePic.setChecked(Constant.CAMERA_SAVE_ORIGINAL_PHOTO);


        mDatabind.llCameraPic.setVisibility(Constant.CAMERA_SAVE_PHOTO_VIDEO ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onBindViewClick() {
        //是否开启标记 要对图片是否进行单独编辑 对于保洁员 是默认关闭的
        mDatabind.switchMark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                requestOp();
            }
        });
        //是否标记

        //照片保存到手机相册
        mDatabind.switchSaveSystemPic.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mDatabind.llCameraPic.setVisibility(isChecked ? View.VISIBLE : View.GONE);
                requestOp();
            }
        });
        //保存原始照片
        mDatabind.switchSavePic.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                requestOp();
            }
        });
    }

    private void requestOp() {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("is_mark", mDatabind.switchMark.isChecked() ? "1" : "2");
        hashMap.put("is_save_to_phone", mDatabind.switchSaveSystemPic.isChecked() ? "1" : "2");
        hashMap.put("is_save_origin_pic", mDatabind.switchSavePic.isChecked() ? "1" : "2");
        mViewModel.requestPhotoSetting(hashMap);
    }


    @Override
    public void onRequestSuccess() {
        mViewModel.getSettingState().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                ToastUtil.show("操作成功");
                Constant.CAMERA_MARK_PHOTO = mDatabind.switchMark.isChecked();
                Constant.CAMERA_SAVE_PHOTO_VIDEO = mDatabind.switchSaveSystemPic.isChecked();
                Constant.CAMERA_SAVE_ORIGINAL_PHOTO = mDatabind.switchSavePic.isChecked();
            }
        });
    }
}