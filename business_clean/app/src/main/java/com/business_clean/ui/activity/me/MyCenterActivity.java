package com.business_clean.ui.activity.me;


import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;

import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.config.AsyncRequestUtil;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.ext.LoadingDialogExtKt;
import com.business_clean.app.flutter.FlutterManager;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.MarketUtils;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.app.util.appupdate.AppCheckUpdateUtil;
import com.business_clean.data.dao.WaterPhotoData;
import com.business_clean.data.dao.WatermarkPhotoDatabaseManager;
import com.business_clean.data.mode.appupdate.DialogAlertNoticeInfo;
import com.business_clean.data.mode.appupdate.NewVersionInfo;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.WorkRulesEntity;
import com.business_clean.databinding.ActivityMyCenterBinding;
import com.business_clean.ui.activity.ErrorActivity;
import com.business_clean.ui.activity.camera.WaterUploadRecordActivity;
import com.business_clean.ui.activity.main.MainActivity;
import com.business_clean.viewmodel.request.MyCenterViewModel;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.lxj.xpopup.interfaces.OnSelectListener;

import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;
import me.hgj.mvvmhelper.util.LogXmManager;

public class MyCenterActivity extends BaseActivity<MyCenterViewModel, ActivityMyCenterBinding> {

    private int changeClickCount = 0;//切换环境

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        mViewModel.requestUserInfoData();//请求用户信息
        mViewModel.requestWorkRules();//请求用户信息
        mToolbar.setTitle("个人中心");
        UserInfo userInfo = App.getAppViewModelInstance().getUserInfo().getValue();
        if (userInfo != null && userInfo.getUser() != null) {
            mDatabind.tvCenterName.setText(userInfo.getUser().getUser_name());
            mDatabind.tvCenterDepartment.setText(userInfo.getUser().getJob_name());
//            mDatabind.tvCenterPost.setText(userInfo.getUser().getJob_name());
            mDatabind.ivCenterAvatar.setAvatar("", userInfo.getUser().getUser_name());
        }

        mDatabind.tvUpdate.setText(AppUtils.getAppVersionName());

        mDatabind.tvCache.setText((getAllSize()));

        mDatabind.ivCenterAvatar.getAvatarText().setTextSize(18f);

        //设置保存到本地
        if (MMKVHelper.getInt(ConstantMMVK.SAVE_PHOTO_ALBUM, 0) == 1) {
            mDatabind.switchSavePic.setChecked(true);
        } else {
            mDatabind.switchSavePic.setChecked(false);
        }

        //设置打卡
        if (MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK, 0) == 1) {
            mDatabind.switchSelfClock.setChecked(true);
        } else {
            mDatabind.switchSelfClock.setChecked(false);
        }

        //工作拍照
        if (MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, 0) == 1) {
            mDatabind.switchWorkCamera.setChecked(true);
        } else {
            mDatabind.switchWorkCamera.setChecked(false);
        }

        //设置底部企业信息
        if (App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getCompany() != null) {
            mDatabind.tvCompanyName.setText(App.getAppViewModelInstance().getUserInfo().getValue().getCompany().getCompany_name());
            mDatabind.tvCompanyNameAll.setText(App.getAppViewModelInstance().getUserInfo().getValue().getCompany().getFull_name());
//            mDatabind.ivCompany.setAvatar("", "");
//            mDatabind.ivCompany.getAvatarText().setTextSize(16f);
        }

        //管理员、超管、大区经理、人事等总部的人员不需要显示工作拍照 其他都显示
        if (Constant.ROLE_LEADER || Constant.ROLE_CLEANER || Constant.ROLE_PROJECT_OWNER) {
            mDatabind.llWorkCamera.setVisibility(View.VISIBLE);
            mDatabind.llSelfClock.setVisibility(View.VISIBLE);
            mDatabind.view1.setVisibility(View.VISIBLE);
            mDatabind.view2.setVisibility(View.VISIBLE);
        } else {
            mDatabind.llWorkCamera.setVisibility(View.GONE);
            mDatabind.llSelfClock.setVisibility(View.GONE);
            mDatabind.view1.setVisibility(View.GONE);
            mDatabind.view2.setVisibility(View.GONE);
        }


        NetUrl.defaultUrl = MMKVHelper.getString(ConstantMMVK.ENVIRONMENT_BASE_URL, NetUrl.SERVER_URL_RELEASE);

        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            mDatabind.llSettingProxy.setVisibility(View.GONE);
            mDatabind.llBottomCompanyInfo.setVisibility(View.GONE);
        } else {
            mDatabind.llSettingProxy.setVisibility(View.VISIBLE);
            mDatabind.llBottomCompanyInfo.setVisibility(View.VISIBLE);
        }
    }

    public String getAllSize() {
        return formatFileSize(getTotalFileSize(FileUtils.getSize(Constant.INTERNAL_LUBAN_PATH))
                + getTotalFileSize((Constant.INTERNAL_MMKV_PATH))
                + getTotalFileSize((Constant.INTERNAL_PHOTO_PATH))
                + getTotalFileSize((Constant.INTERNAL_VIDEO_PATH))
        );
    }

    public long getTotalFileSize(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            return 0; // 如果路径不存在或者不是文件夹，返回大小为0
        }

        long totalSize = 0;
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    totalSize += file.length(); // 累加文件大小
                } else if (file.isDirectory()) {
                    totalSize += getTotalFileSize(file.getAbsolutePath()); // 递归获取子文件夹大小
                }
            }
        }
        return totalSize;
    }

    /**
     * 转换文件大小
     *
     * @param fileS
     * @return
     */
    public String formatFileSize(long fileS) {
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        String wrongSize = "0";
        if (fileS == 0) {
            return wrongSize;
        }
        if (fileS < 1024) {
            fileSizeString = df.format((double) fileS) + "";
        } else if (fileS < 1048576) {
            fileSizeString = df.format((double) fileS / 1024) + "KB";
        } else if (fileS < 1073741824) {
            fileSizeString = df.format((double) fileS / 1048576) + "MB";
        } else {
            fileSizeString = df.format((double) fileS / 1073741824) + "GB";
        }
        return fileSizeString;
    }

    @Override
    public void onBindViewClick() {
        //设置flutter 的代理
        mDatabind.llSettingProxy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("settingProxyPage")
                        .arguments(hashMap)
                        .build());
            }
        });
        //打卡方式
        mDatabind.switchSelfClock.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MMKVHelper.putInt(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK, mDatabind.switchSelfClock.isChecked() ? 1 : 2);
            }
        });

        //工作拍照
        mDatabind.switchWorkCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MMKVHelper.putInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, mDatabind.switchWorkCamera.isChecked() ? 1 : 2);
                App.getAppViewModelInstance().getRefreshWaterTag().postValue(true);
            }
        });


        //联系方式
        mDatabind.llContact.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.gotoBaseWebActivity(Constant.CONTACT);
            }
        });

        //服务协议、隐私政策
        mDatabind.llMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String[] strings = {"服务协议", "隐私政策", "注销账号"};
                CommonUtils.showBottomListWith(MyCenterActivity.this, 0, "", strings, new OnSelectListener() {
                    @Override
                    public void onSelect(int position, String text) {
                        switch (position) {
                            case 0:
                                CommonUtils.gotoBaseWebActivity(Constant.SERVICE_AGREEMENT);
                                break;
                            case 1:
                                CommonUtils.gotoBaseWebActivity(Constant.PRIVACY_POLICY);
                                break;
                            case 2:
                                CommonUtils.gotoBaseWebActivity(Constant.USER_OFF);
                                break;
                        }
                    }
                });
            }
        });

        //我的考勤
//        mDatabind.llAttendance.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                ActivityForwardUtil.startActivity(MyAttendanceActivity.class);
//            }
//        });

        //个人相册
//        mDatabind.llMyAlbum.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                ActivityForwardUtil.startActivity(MyPhotosActivity.class);
//            }
//        });

        //离线上传记录
        mDatabind.llUploadRecord.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityForwardUtil.startActivity(WaterUploadRecordActivity.class);
            }
        });


        mDatabind.llLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.showGeneralDialog(MyCenterActivity.this, "退出登录", "是否退出登录？",
                        "取消", "确定", null, new OnDialogConfirmListener() {
                            @Override
                            public void onConfirm() {
                                WatermarkPhotoDatabaseManager.getInstance(MyCenterActivity.this).deleteAll(WaterPhotoData.class);
                                CommonUtils.logoutApp();
                            }
                        });
            }
        });

        mDatabind.llClean.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.showGeneralDialog(MyCenterActivity.this, "清除缓存", "是否确定清除缓存，清除后无法恢复",
                        "取消", "确定", null, new OnDialogConfirmListener() {
                            @Override
                            public void onConfirm() {
                                mDatabind.llClean.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        ToastUtil.show("清空成功");
                                        FileUtils.deleteFilesInDir(Constant.INTERNAL_PHOTO_PATH);
                                        FileUtils.deleteFilesInDir(Constant.INTERNAL_VIDEO_PATH);
                                        FileUtils.deleteFilesInDir(Constant.INTERNAL_LUBAN_PATH);
                                        mDatabind.tvCache.setText((getAllSize()));
                                    }
                                }, 1500);
                            }
                        });
            }
        });

        //版本更新
        mDatabind.llUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mViewModel.requestCheckApp();
            }
        });


        //复制token
        mDatabind.withAppCopyToken.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String token = MMKVHelper.getString(ConstantMMVK.TOKEN);
                CommonUtils.copyToClipboard(MyCenterActivity.this, token);
            }
        });
        //复制项目的uuid
        mDatabind.withAppCopyProjectUuid.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.copyToClipboard(MyCenterActivity.this, App.getAppViewModelInstance().getProjectInfo().getValue().getUuid());
            }
        });

        //关闭一键登陆的功能
        mDatabind.withAutoLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!MMKVHelper.getBoolean(ConstantMMVK.AUTO_LOGIN, false)) {
                    MMKVHelper.putBoolean(ConstantMMVK.AUTO_LOGIN, true);
                    ToastUtil.show("关闭一键登陆了，不会烦人了");
                } else {
                    MMKVHelper.putBoolean(ConstantMMVK.AUTO_LOGIN, false);
                    ToastUtil.show("又开启一键登陆了，开始烦人了");
                }
            }
        });

        //切换环境
        mDatabind.ivCenterAvatar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                changeClickCount++;
                if (changeClickCount > 10) {
                    ToastUtil.show(changeClickCount);
                }
                if (changeClickCount == 15) {
                    mDatabind.withAppCopyToken.setVisibility(View.VISIBLE);
                    mDatabind.withAppCopyProjectUuid.setVisibility(View.VISIBLE);
                    mDatabind.withAutoLogin.setVisibility(View.VISIBLE);
                    changeClickCount = 0;
                }
            }
        });


        //是否保存图片
        mDatabind.switchSavePic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Constant.CAMERA_SAVE_PHOTO_VIDEO = mDatabind.switchSelfClock.isChecked();
                //针对本地相册 进行保存处理
                MMKVHelper.putInt(ConstantMMVK.SAVE_PHOTO_ALBUM, mDatabind.switchSavePic.isChecked() ? 1 : 2);
            }
        });

        //上报本地日志记录
        mDatabind.llUploadLog.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //https://pccmedia.jiazhengye.cn/log/android/clean/20250107/13810250440/1736220597661 路径是这个
//                LogUtils.e("" + LogXmManager.getLogsText(MyCenterActivity.this));
                LoadingDialogExtKt.showLoadingExt(MyCenterActivity.this, "上传中");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                long timeMillis = System.currentTimeMillis();
                String day = sdf.format(timeMillis);
                String fileName = "log/android/clean/" + day + "/" + MMKVHelper.getString(ConstantMMVK.USER_PHONE) + "/" + System.currentTimeMillis() + ".log";
                UploadFileHelper.getInstance().uploadLogFile(MyCenterActivity.this, LogXmManager.getLogFilePath(MyCenterActivity.this),
                        UploadFileHelper.PATH_HEADER_LOG, fileName, new UploadFileHelper.UploadListener() {
                            @Override
                            public void onUploadSuccess(String response) {
                                LogExtKt.logE("上传成功---> " + response, "");
                                LogXmManager.clearLogFile(MyCenterActivity.this);
                                if (!MyCenterActivity.this.isFinishing()) {
                                    LoadingDialogExtKt.dismissLoadingExt(MyCenterActivity.this);
                                }
                                ToastUtil.show("上传成功，感谢您的反馈～");
                            }

                            @Override
                            public void onUploadFailed(String error) {
                                ToastUtil.show("上传失败");
                                if (!MyCenterActivity.this.isFinishing()) {
                                    LoadingDialogExtKt.dismissLoadingExt(MyCenterActivity.this);
                                }
                            }

                            @Override
                            public void onUploadProgress(int progress) {

                            }


                        });
            }
        });
    }

    /**
     * 显示一个升级弹窗
     *
     * @param newVersionInfo
     */
    private void showUpdateDialog(NewVersionInfo newVersionInfo) {
        DialogAlertNoticeInfo dialogAlertNoticeInfo = new DialogAlertNoticeInfo();
        dialogAlertNoticeInfo.setTitle(newVersionInfo.getVersion() + "版本已经准备好了");
        dialogAlertNoticeInfo.setDescribe("升级一下新功能，也许能帮助到您");
        dialogAlertNoticeInfo.setContent(newVersionInfo.getDesc());
        dialogAlertNoticeInfo.setSub_title_color(ContextCompat.getColor(this, R.color.base_primary_warning));
        dialogAlertNoticeInfo.setConfirm_button("立即更新");
        dialogAlertNoticeInfo.setClose_button("取消");//即代表忽略本次版本更新
        dialogAlertNoticeInfo.setDismissOnBackPressed(false);
        dialogAlertNoticeInfo.setDismissOnTouchOutside(false);

        CommonUtils.showSystemUpdateAlertPop(this, dialogAlertNoticeInfo, new OnDialogCancelListener() {
            @Override
            public void onCancel() {
                if (!TextUtils.isEmpty(newVersionInfo.getVersion())) {
                    MMKVHelper.putInt(ConstantMMVK.APP_UPDATE_VERSION_CODE, Integer.parseInt(newVersionInfo.getVersion().replace(".", "")));
                }
            }
        }, new OnDialogConfirmListener() {
            @Override
            public void onConfirm() {
                MarketUtils.getTools().startMarket(MyCenterActivity.this);
            }
        });
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getNewVersionInfo().observe(this, new Observer<NewVersionInfo>() {
            @Override
            public void onChanged(NewVersionInfo newVersionInfo) {
                String newVersion = newVersionInfo.getVersion();
                if (!TextUtils.isEmpty(newVersion)) {
                    if (AppCheckUpdateUtil.compareVersion(newVersion, AppUtils.getAppVersionName()) > 0) {
//                        AppCheckUpdateUtil.checkVersionNew(true, false);
                        // 获取发布品牌 ID 列表
                        List<String> brandIdList = newVersionInfo.getBrand_id_list();

                        // 获取当前手机厂商并映射为品牌 ID
                        String manufacturer = Build.MANUFACTURER.toLowerCase(); // 转为小写
                        String currentBrandId = CommonUtils.getBrandIdByManufacturer(manufacturer);

                        // 判断当前手机厂商是否在发布品牌列表中
                        if (brandIdList.contains(currentBrandId)) {
                            // 符合条件：弹窗提示用户更新 跳转应用市场
                            showUpdateDialog(newVersionInfo);
                        } else {
                            // 不符合条件：静默下载
                            AppCheckUpdateUtil.checkVersionNew(true, true);
                        }
                    } else {
                        ToastUtil.show("已经是最新版本了");
                    }
                }
            }
        });

        mViewModel.getUserOne().observe(this, new Observer<UserInfo>() {
            @Override
            public void onChanged(UserInfo userInfo) {
                MMKVHelper.putString(ConstantMMVK.USER_INFO, JSON.toJSONString(userInfo));
                CommonUtils.updateLocalUserData(userInfo);
            }
        });


        mViewModel.getWorkRulesEntity().observe(this, new Observer<WorkRulesEntity>() {
            @Override
            public void onChanged(WorkRulesEntity workRulesEntity) {
                CommonUtils.updateCompanyConfig(workRulesEntity);
            }
        });
    }
}