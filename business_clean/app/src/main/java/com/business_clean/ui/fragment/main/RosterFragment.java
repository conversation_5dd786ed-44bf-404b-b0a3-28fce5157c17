package com.business_clean.ui.fragment.main;

import static com.business_clean.app.config.Constant.PAGE_SIZE;
import static com.business_clean.app.config.Constant.TYPE_INIT_DATA;
import static com.business_clean.app.config.Constant.TYPE_INIT_LOAD_MORE;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PhoneUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.callback.OnDialogEditTextBottomListener;
import com.business_clean.app.callback.OnDialogSelectListener;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.CheckListDataUtil;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.app.util.DownLoadHelper;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.permission.PermissionInterceptor;
import com.business_clean.app.weight.dialog.CustomEditTextBottomPopup;
import com.business_clean.data.initconfig.popup.BaseBottomListEntity;
import com.business_clean.data.mode.BaseDownLoadEntity;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.data.mode.roster.RosterEntity;
import com.business_clean.data.mode.roster.RosterFilterData;
import com.business_clean.databinding.FragmentRosterBinding;
import com.business_clean.ui.activity.personnel.PersonnelDetailActivity;
import com.business_clean.ui.adapter.roster.RosterAdapter;
import com.business_clean.ui.adapter.roster.RosterRowAdapter;
import com.business_clean.viewmodel.request.RosterViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.gyf.immersionbar.ImmersionBar;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.lxj.xpopup.XPopup;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.reactivex.rxjava3.subjects.PublishSubject;
import me.hgj.mvvmhelper.ext.AppExtKt;

public class RosterFragment extends BaseFragment<RosterViewModel, FragmentRosterBinding> {

    private RosterAdapter rosterAdapter;
    private RosterRowAdapter rowAdapter;

    private int page = 1;
    private int requestType;

    private String project_uuid = "";

    private String is_head_office = "0";

    private RosterFilterData filterData;

    // 创建一个 PublishSubject 用于监听输入框的文本变化w
    private PublishSubject<String> searchSubject = PublishSubject.create();


    @Override
    public void onResume() {
        super.onResume();
        ImmersionBar.with(this).titleBar(getMDatabind().llTop).navigationBarColor(R.color.white).statusBarDarkFont(true).init();
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {

        rosterAdapter = new RosterAdapter();

        getMDatabind().list.recyclerView.setLayoutManager(new LinearLayoutManager(getMActivity()));
        getMDatabind().list.recyclerView.addItemDecoration(new DividerItemDecoration(getMActivity()));
        getMDatabind().list.recyclerView.setAdapter(rosterAdapter);
        getMDatabind().list.recyclerView.setBackgroundResource(R.color.white);

        View headerView = getLayoutInflater().inflate(R.layout.header_empty, null);
        rosterAdapter.setHeaderView(headerView);

        rowAdapter = new RosterRowAdapter();
        getMDatabind().recyclerRow.setLayoutManager(new LinearLayoutManager(getMActivity()));
        getMDatabind().recyclerRow.setAdapter(rowAdapter);

        getMDatabind().list.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestMore();
            }

            @Override
            public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestOne();
            }
        });


        //如果当前的角色是保洁、或者领班的情况下 隐藏 row导航 增加员工 导出表格
        if (Constant.ROLE_CLEANER || Constant.ROLE_LEADER) {
            getMDatabind().ivFilter.setVisibility(View.GONE);
            getMDatabind().ivLeft.setVisibility(View.GONE);
            getMDatabind().ivRightSide.setVisibility(View.GONE);
            ///领班可以看到
            getMDatabind().ivRight.setVisibility(Constant.ROLE_LEADER ? View.VISIBLE : View.GONE);
        } else {
            //其他角色OK 可以进来 但是 针对ivLeft 需要增加个大区经理的判断，如果是大区经理，那么就不显示ivLeft 否则就显示
            if (Constant.ROLE_PROJECT_OWNER) {//项目负责人不要侧边栏
                getMDatabind().ivLeft.setVisibility(View.GONE);
            } else {
                getMDatabind().ivLeft.setVisibility(View.VISIBLE);
                //从本地库种获取状态 去设置
                if (MMKVHelper.getBoolean(ConstantMMVK.ROSTER_MENU_STATUS, false)) { //如果默认是 false 那么就默认关闭的
                    getMDatabind().ivLeft.setImageResource(R.mipmap.icon_base_show);
                    getMDatabind().llRowLayout.setVisibility(View.VISIBLE);
                } else {
                    getMDatabind().llRowLayout.setVisibility(View.GONE);
                    getMDatabind().ivLeft.setImageResource(R.mipmap.icon_base_hide);
                }
            }
        }

        //如果是项目负责人、领班、保洁员 只能看到当前项目下的花名册
        if (Constant.ROLE_PROJECT_OWNER || Constant.ROLE_LEADER || Constant.ROLE_CLEANER && App.getAppViewModelInstance().getProjectInfo() != null) {
            project_uuid = App.getAppViewModelInstance().getProjectInfo().getValue().getUuid();
        }
    }

    @Override
    public void lazyLoadData() {
        requestOne();
    }

    @Override
    public void initObserver() {
        //监听创建成员成功
        App.getAppViewModelInstance().getRefreshMember().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                requestOne();
            }
        });
        //监听总部成员
        App.getAppViewModelInstance().getRefreshContactManagerList().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                requestOne();
            }
        });

        //监听筛选的回调
        App.getAppViewModelInstance().getFilterRosterFilterData().observe(this, new Observer<RosterFilterData>() {
            @Override
            public void onChanged(RosterFilterData rosterFilterData) {
                getMDatabind().ivFilter.setImageResource(rosterFilterData.isReset() ? R.mipmap.icon_base_roster_filter : R.mipmap.icon_roster_filter_select);
                if (rosterFilterData.isReset()) {
                    filterData = null;
                } else {
                    LogUtils.e(filterData);
                    filterData = rosterFilterData;
                }
                requestOne();
            }
        });

        //监听输入框的内容
        getMDatabind().etRosterSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                searchSubject.onNext(s.toString());

            }
        });
        // 使用 debounce 操作符实现延迟搜索
        searchSubject
                .debounce(1, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(query -> {
                    // 在这里执行搜索请求，比如调用网络接口搜索
                    requestOne();
                });
    }

    @Override
    public void onBindViewClick() {
        //弹出搜索的界面
        getMDatabind().llSearchLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                new XPopup.Builder(getMActivity())
                        .autoOpenSoftInput(true)
                        .asCustom(new CustomEditTextBottomPopup(getMActivity(), getMDatabind().tvRosterRowSearch.getText().toString(), new OnDialogEditTextBottomListener() {
                            @Override
                            public void onDialogEditTextBottom(String text) {
                                if (rowAdapter != null) {
                                    rowAdapter.filter(text);
                                    //然后获取Adapter的第一个去请求数据
                                    checkListRequest();
                                }
                                if (!TextUtils.isEmpty(text)) {
                                    getMDatabind().tvRosterRowSearch.setText(text);
                                } else {
                                    getMDatabind().tvRosterRowSearch.setText("");
                                }
                            }
                        }))
                        .show();
            }
        });
        //进去筛选页面
        getMDatabind().ivFilter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                if (filterData != null) {
                    hashMap.put("filterData", JSON.toJSONString(filterData));
                }
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("RosterFilterRulesPage")
                        .arguments(hashMap)
                        .build());
            }
        });
        //点击
        rowAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                rowClick(rowAdapter.getData().get(position).getUuid());
            }
        });

        rosterAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getId() == R.id.iv_item_roster_call) {
                    PhoneUtils.dial(rosterAdapter.getData().get(position).getMobile());
                }
            }
        });

        rosterAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                if (Constant.ROLE_CLEANER) {//如果是保洁员，不让点击
                    return;
                }
                CommonToFlutter.gotoFlutterStaffWebOnePage(rosterAdapter.getData().get(position).getUuid());
//                Bundle bundle = new Bundle();
//                bundle.putString("uuid", rosterAdapter.getData().get(position).getUuid());
//                ActivityForwardUtil.startActivity(PersonnelDetailActivity.class, bundle);
            }
        });
        //权限
        getMDatabind().ivLeft.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_ID.equals(Constant.ROLE_CLEAN_ID) || Constant.ROLE_ID.equals(Constant.ROLE_LEADER_ID) || Constant.ROLE_ID.equals(Constant.ROLE_PROJECT_OWNER_ID)) {
                    ToastUtil.show("暂无权限操作");
                    return;
                }

                if (getMDatabind().llRowLayout.getVisibility() == View.VISIBLE) {
                    getMDatabind().llRowLayout.setVisibility(View.GONE);
                    getMDatabind().ivLeft.setImageResource(R.mipmap.icon_base_hide);
                    MMKVHelper.putBoolean(ConstantMMVK.ROSTER_MENU_STATUS, false);
                } else {
                    getMDatabind().llRowLayout.setVisibility(View.VISIBLE);
                    getMDatabind().ivLeft.setImageResource(R.mipmap.icon_base_show);
                    MMKVHelper.putBoolean(ConstantMMVK.ROSTER_MENU_STATUS, true);
                }
            }
        });
        //增加
        getMDatabind().ivRight.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_ID.equals(Constant.ROLE_CLEAN_ID)) {
                    ToastUtil.show("暂无权限操作");
                    return;
                }
                gotoAddProjectStaffPage("");
            }
        });
        //分享
        getMDatabind().ivRightSide.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_ID.equals(Constant.ROLE_CLEAN_ID) || Constant.ROLE_ID.equals(Constant.ROLE_LEADER_ID)) {
                    ToastUtil.show("暂无权限操作");
                    return;
                }

                XXPermissions.with(getActivity())
                        .permission(AppExtKt.getExternalStorage())
                        .interceptor(new PermissionInterceptor("需要下载文件到您的手机，便于您分享，所以需要用到存储权限"))
                        .request(new OnPermissionCallback() {
                            @Override
                            public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                if (allGranted) {
                                    mViewModel.requestDownloadExcel("1", is_head_office, project_uuid);
                                }
                            }
                        });


            }
        });
    }

    /**
     * 添加成员
     *
     * @param uuid
     */
    private void gotoAddProjectStaffPage(String uuid) {
        CommonToFlutter.gotoFlutterAddStaffPage("", "", 0);
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getRosterManager().observe(this, new Observer<RosterEntity>() {
            @Override
            public void onChanged(RosterEntity rosterEntity) {
                List<ProjectMangerList> filterList = rosterEntity.getFilter_list();
                if (TextUtils.isEmpty(getMDatabind().tvRosterRowSearch.getText().toString())) {
                    if (filterList != null) {
                        for (ProjectMangerList filterListBean : filterList) {
                            if ("全部".equals(filterListBean.getProject_short_name())) {
                                filterListBean.setUuid("0");
                            } else if ("总部".equals(filterListBean.getProject_short_name())) {
                                filterListBean.setUuid("1");
                            }
                        }
                        rowAdapter.setList(filterList); // 更新适配器数据
                    } else {
                        rowAdapter.setList(null);
                    }
                }

                switch (requestType) {
                    case TYPE_INIT_DATA:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkInitData(rosterEntity.getList(), rosterAdapter, getMDatabind().list.refreshLayout)) {
                            rosterAdapter.setList(rosterEntity.getList());
                        }
                        break;
                    case TYPE_INIT_LOAD_MORE:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkLoadMoreData(rosterEntity.getList(), rosterAdapter, getMDatabind().list.refreshLayout)) {
                            rosterAdapter.addData(rosterEntity.getList());
                        }
                        break;
                }
            }
        });
        //监听请求内容
        mViewModel.getDownLoadEntity().observe(this, new Observer<BaseDownLoadEntity>() {
            @Override
            public void onChanged(BaseDownLoadEntity baseDownLoadEntity) {
                String fileName = App.getAppViewModelInstance().getUserInfo().getValue().getCompany().getCompany_name() + "_" + "花名册" + "_" + TimeUtils.date2String(TimeUtils.getNowDate(), "yyyy-MM-dd-hh-mm");
                DownLoadHelper.downloadFile(getActivity(), baseDownLoadEntity.getDownload_url(), null, fileName, true);
            }
        });
    }


    /**
     * 搜索后检查下Adapter 是否有数据，取第一个
     */
    private void checkListRequest() {
        if (rowAdapter != null && rowAdapter.getData().size() > 0) {
            rowClick(rowAdapter.getData().get(0).getUuid());
        }
    }


    private void rowClick(String uuid) {
        switch (uuid) {
            case "0":
                project_uuid = "";
                is_head_office = "0";
                break;
            case "1":
                is_head_office = "1";
                project_uuid = "";
                break;
            default:
                is_head_office = "2";
                project_uuid = uuid;
                break;
        }
        rowAdapter.updateItem(uuid);
        requestOne();
    }

    private void requestOne() {
        requestType = TYPE_INIT_DATA;
        page = 1;
        mViewModel.getRosterList(getHashMap());
    }

    private void requestMore() {
        requestType = TYPE_INIT_LOAD_MORE;
        page++;
        mViewModel.getRosterList(getHashMap());
    }


    private HashMap<String, String> getHashMap() {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("page", "" + page);
        hashMap.put("size", "" + PAGE_SIZE);
        hashMap.put("status", "1");//状态 0全部 1在职 2离职
        hashMap.put("is_head_office", "" + is_head_office);//是否总部员工1是 2否 0不区分
        if (!TextUtils.isEmpty(project_uuid)) {
            hashMap.put("project_uuid", "" + project_uuid);
        }
        if (getMDatabind().etRosterSearch.getText().length() > 0) {
            hashMap.put("keyword", "" + getMDatabind().etRosterSearch.getText().toString());
        }
        //新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
        hashMap.put("is_head_office_project", CommonUtils.checkRoleHeadOffice() ? "1" : "2");
        if (filterData != null) {
            if (!TextUtils.isEmpty(filterData.getSex())) {
                hashMap.put("sex", "" + filterData.getSex());
            }
            if (!TextUtils.isEmpty(filterData.getMin_age())) {
                hashMap.put("min_age", "" + filterData.getMin_age());
            }
            if (!TextUtils.isEmpty(filterData.getMax_age())) {
                hashMap.put("max_age", "" + filterData.getMax_age());
            }
            if (!TextUtils.isEmpty(filterData.getStart_date())) {
                LogUtils.e("我是合约开始日期 + " + filterData.getStart_date());
                LogUtils.e("我是合约开始日期 + " + filterData.getStart_date().replace(" ~ ", "#"));
                hashMap.put("contract_start_date", "" + filterData.getStart_date().replace(" ~ ", "#"));
            }
            if (!TextUtils.isEmpty(filterData.getEnd_date())) {
                hashMap.put("contract_end_date", "" + filterData.getEnd_date().replace(" ~ ", "#"));
            }
            if (!TextUtils.isEmpty(filterData.getPic_status())) {
                hashMap.put("is_has_avatar", "" + filterData.getPic_status());
            }
            if (!TextUtils.isEmpty(filterData.getBank_status())) {
                hashMap.put("is_has_bank_card", "" + filterData.getBank_status());
            }
            if (!TextUtils.isEmpty(filterData.getHealth_status())) {
                hashMap.put("is_has_healthy_card", "" + filterData.getHealth_status());
            }
            if (!TextUtils.isEmpty(filterData.getRecode_check())) {
                hashMap.put("is_has_no_crime", "" + filterData.getRecode_check());
            }
            if (!TextUtils.isEmpty(filterData.getIs_has_contract())) {
                hashMap.put("is_has_contract", "" + filterData.getIs_has_contract());
            }
            if (!TextUtils.isEmpty(filterData.getRole_id())) {
                hashMap.put("role_id", "" + filterData.getRole_id());
            }
            if (!TextUtils.isEmpty(filterData.getGroup_uuid())) {
                hashMap.put("group_uuid", "" + filterData.getGroup_uuid());
            }
            //insurance_status 保险状态 1无需购买 2待购买 3已购买
            if (!TextUtils.isEmpty(filterData.getInsure_status())) {
                hashMap.put("insurance_status", "" + filterData.getInsure_status());
            }
            //insurance_type 参保类型 1不参保 2社保 3商业保险
            if (!TextUtils.isEmpty(filterData.getInsurance_type())) {
                hashMap.put("insurance_type", "" + filterData.getInsurance_type());
            }
            //是否有身份证 1是 2否
            if (!TextUtils.isEmpty(filterData.getIs_has_id_card())) {
                hashMap.put("is_has_id_card", "" + filterData.getIs_has_id_card());
            }
            //是否有信用风险 1是 2否 0未查询
            if (!TextUtils.isEmpty(filterData.getCredit_inquiry_status())) {
                if ("1".equals(filterData.getCredit_inquiry_status())) {
                    hashMap.put("is_has_credit_wind", "2");
                } else {
                    hashMap.put("is_has_credit_wind", "1");
                }
            }
        }
        return hashMap;
    }
}
