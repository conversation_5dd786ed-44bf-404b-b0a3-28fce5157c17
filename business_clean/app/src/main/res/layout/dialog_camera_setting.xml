<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="10dp">

            <TextView
                android:visibility="gone"
                android:id="@+id/iv_dialog_camera_setting_flash"
                android:layout_width="match_parent"
                android:layout_height="@dimen/font_size_48"
                android:gravity="center"
                android:text="切换闪光灯状态(始终开启)"
                android:textColor="#1a1a1a"
                android:textSize="@dimen/font_size_16" />

<!--            <include layout="@layout/view_line" />-->

            <TextView
                android:id="@+id/iv_dialog_camera_setting_change"
                android:layout_width="match_parent"
                android:visibility="gone"
                android:layout_height="@dimen/font_size_48"
                android:gravity="center"
                android:text="切换前后置摄像头"
                android:textColor="#1a1a1a"
                android:textSize="@dimen/font_size_16" />

<!--            <include layout="@layout/view_line" />-->

            <TextView
                android:id="@+id/iv_dialog_camera_save_album"
                android:layout_width="match_parent"
                android:layout_height="@dimen/font_size_48"
                android:gravity="center"
                android:text="保存到手机相册"
                android:textColor="#1a1a1a"
                android:textSize="@dimen/font_size_16" />


<!--            <include layout="@layout/view_line" />-->

<!--            <TextView-->
<!--                android:id="@+id/iv_dialog_camera_save_original"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/font_size_48"-->
<!--                android:gravity="center"-->
<!--                android:text="保存原始照片"-->
<!--                android:textColor="#1a1a1a"-->
<!--                android:textSize="@dimen/font_size_16" />-->


            <include layout="@layout/view_line" />

            <TextView
                android:id="@+id/iv_dialog_camera_user"
                android:layout_width="match_parent"
                android:layout_height="@dimen/font_size_48"
                android:gravity="center"
                android:text="个人中心"
                android:textColor="#1a1a1a"
                android:textSize="@dimen/font_size_16" />

        </LinearLayout>

    </LinearLayout>


    <TextView
        android:id="@+id/tv_dialog_camera_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:padding="10dp"
        android:text="取消"
        android:textColor="@color/base_primary_text_title"
        android:textSize="@dimen/font_size_16"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white" />
</LinearLayout>