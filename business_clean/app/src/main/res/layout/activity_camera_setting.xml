<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_primary_bg_page"
        android:orientation="vertical"
        tools:context=".ui.activity.camera.CameraSettingActivity">

        <com.business_clean.app.weight.CustomTabBar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:titleText="拍照设置" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:orientation="vertical"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            android:paddingBottom="10dp"
            android:visibility="gone"
            app:bl_corners_radius="10dp"
            app:bl_solid_color="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center|left"
                    android:text="拍摄后进入标记环节"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_14" />

                <Switch
                    android:id="@+id/switch_mark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="不进入标记环节时，系统会自动保存照片/视频"
                android:textColor="@color/base_primary_text_caption"
                android:textSize="@dimen/font_size_12" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_camera_system_pic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:orientation="vertical"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            android:paddingBottom="10dp"
            app:bl_corners_radius="10dp"
            app:bl_solid_color="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center|left"
                        android:text="保存照片/视频到手机相册"
                        android:textColor="@color/base_primary_text_title"
                        android:textSize="@dimen/font_size_14" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="开启后始终会保存水印照片和视频"
                        android:textColor="@color/base_primary_text_caption"
                        android:textSize="@dimen/font_size_12" />
                </LinearLayout>


                <Switch
                    android:id="@+id/switch_save_system_pic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>


        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_camera_pic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            android:paddingBottom="10dp"
            app:bl_corners_radius="10dp"
            app:bl_solid_color="@color/white">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center|left"
                    android:text="保存原始照片"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_14" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_weight="1"
                    android:gravity="center|left"
                    android:text="目前无法保存无水印视频"
                    android:textColor="@color/base_primary_text_caption"
                    android:textSize="@dimen/font_size_12" />
            </LinearLayout>

            <Switch
                android:id="@+id/switch_save_pic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </LinearLayout>
</layout>