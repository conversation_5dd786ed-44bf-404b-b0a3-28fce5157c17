<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_primary_bg_page"
            android:orientation="vertical"
            tools:context=".ui.activity.me.MyCenterActivity">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="20dp"
                android:paddingBottom="10dp"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="@color/white">

                <TextView
                    android:id="@+id/tv_center_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_22"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_center_department"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_center_name"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_16" />

                <TextView
                    android:id="@+id/tv_center_post"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_center_department"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_16"
                    android:visibility="gone" />

                <com.business_clean.app.weight.CustomAvatarView
                    android:id="@+id/iv_center_avatar"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_alignParentRight="true" />
            </RelativeLayout>


            <LinearLayout
                android:id="@+id/ll_upload_record"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_corners_bottomLeftRadius="10dp"
                app:bl_corners_bottomRightRadius="10dp"
                app:bl_corners_topLeftRadius="10dp"
                app:bl_corners_topRightRadius="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="离线上传记录"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="vertical"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="@color/white">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:text="保存相片到手机"
                        android:textColor="@color/base_primary_text_title"
                        android:textSize="@dimen/font_size_15" />


                    <Switch
                        android:id="@+id/switch_save_pic"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <View
                    android:id="@+id/view_1"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="@color/base_primary_line_b"
                    android:visibility="visible" />

                <LinearLayout
                    android:id="@+id/ll_work_camera"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:text="开启“工作拍照”"
                        android:textColor="@color/base_primary_text_title"
                        android:textSize="@dimen/font_size_15" />


                    <Switch
                        android:id="@+id/switch_work_camera"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <View
                    android:id="@+id/view_2"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="@color/base_primary_line_b"
                    android:visibility="visible" />

                <LinearLayout
                    android:id="@+id/ll_self_clock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:text="“打卡”需确认"
                        android:textColor="@color/base_primary_text_title"
                        android:textSize="@dimen/font_size_15" />


                    <Switch
                        android:id="@+id/switch_self_clock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/ll_update"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_corners_topLeftRadius="10dp"
                app:bl_corners_topRightRadius="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="版本更新"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />

                <TextView
                    android:id="@+id/tv_update"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/base_primary_text_hint"
                    android:textSize="@dimen/font_size_15"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/base_primary_line_b" />

            <LinearLayout
                android:id="@+id/ll_clean"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="清理缓存"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />

                <TextView
                    android:id="@+id/tv_cache"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/base_primary_text_hint"
                    android:textSize="@dimen/font_size_15"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/base_primary_line_b" />

            <LinearLayout
                android:id="@+id/ll_upload_log"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="上报日志"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />


                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/base_primary_line_b" />

            <com.business_clean.app.weight.WithBaseItemView
                android:id="@+id/with_app_copy_token"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:visibility="gone"
                app:bl_corners_radius="6dp"
                app:left_title="复制token"
                app:viewLineVisible="false" />

            <com.business_clean.app.weight.WithBaseItemView
                android:id="@+id/with_app_copy_project_uuid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:visibility="gone"
                app:bl_corners_radius="6dp"
                app:left_title="复制项目uuid"
                app:viewLineVisible="false" />

            <com.business_clean.app.weight.WithBaseItemView
                android:id="@+id/with_auto_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:visibility="gone"
                app:bl_corners_radius="6dp"
                app:left_title="关闭一键自动登录"
                app:viewLineVisible="false" />

            <LinearLayout
                android:id="@+id/ll_contact"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="联系我们"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />


                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/base_primary_line_b" />

            <LinearLayout
                android:id="@+id/ll_more"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_corners_bottomLeftRadius="10dp"
                app:bl_corners_bottomRightRadius="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="更多"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_setting_proxy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="设置Flutter代理"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/base_primary_text_hint"
                    android:textSize="@dimen/font_size_15"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/ll_logout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="@color/white">


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:text="退出登录"
                    android:textColor="@color/base_primary_text_title"
                    android:textSize="@dimen/font_size_15" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/base_primary_text_hint"
                    android:textSize="@dimen/font_size_15"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:padding="4dp"
                    android:src="@mipmap/icon_base_arrow_gray"
                    android:visibility="visible" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/ll_bottom_company_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_margin="10dp"
                android:background="@color/base_primary_bg_page"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_toRightOf="@+id/iv_company"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/base_primary_text_caption"
                    android:textSize="@dimen/font_size_10" />

                <TextView
                    android:id="@+id/tv_company_name_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_company_name"
                    android:layout_gravity="center"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/base_primary_text_caption"
                    android:textSize="@dimen/font_size_10" />

            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>